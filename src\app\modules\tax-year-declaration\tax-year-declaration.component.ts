import { Component, OnD<PERSON>roy, OnInit } from '@angular/core';
import { Form<PERSON>uilder, FormGroup, Validators } from '@angular/forms';
import { Constant } from '@core/models/constants.enum';
import {
  Column,
  SeDropdownOption,
  SeModal,
  SeModalOutputEvents,
  SeModalService,
  SeProgressModal,
  iDocumentPadoct,
} from 'se-ui-components-mf-lib';
import {
  ContestableActDocument,
  DeclarationTypes,
} from './tax-year-declaration.model';
import { TranslateService } from '@ngx-translate/core';
import { TaxYearDeclarationService } from './services/tax-declaration.service';
import { TaxYearDeclarationEndpointService } from './services/tax-declaration-endpoint.service';
import { BehaviorSubject, Subject, takeUntil } from 'rxjs';
import { Router } from '@angular/router';
import { AppRoutes } from '@core/models/app-routes.enum';
import { StoreService } from '@core/services';

@Component({
  selector: 'app-tax-year-declaration',
  templateUrl: './tax-year-declaration.component.html',
  styleUrls: [],
})
export class TaxYearDeclarationComponent implements OnInit, OnDestroy {
  protected componentForm: FormGroup;
  private destroyed$: Subject<void> = new Subject<void>();
  private currentModelCode: string = '';
  protected optionsModelList: SeDropdownOption[] = [];
  protected optionsTaxYearList: SeDropdownOption[] = [];
  protected readonly functionalModule: string = Constant.NAME;
  protected readonly acceptedFiles = ['csv'];
  protected readonly contestableActDocument: ContestableActDocument[] = [
    {
      type: Constant.TAX_DOCUMENT_TYPE,
      name: Constant.TAX_DOCUMENT_TYPE,
      description: this.translateService.instant(
        'SE_DECINF_MF.MODULE_TAX_YEAR_DECLARATION.MODEL_523_PANEL.NOM_DOCUMENT',
      ),
      allowedFiles: this.acceptedFiles,
    },
  ];
  // TABLE COLUMNS
  protected tableColumns: Column[] =
    this.taxYearDeclarationService.getTableColumns();
  protected modalTableColumns: Column[] =
    this.taxYearDeclarationService.getModalTableColumns();
  // DELETE FILE
  private deleteFileSubject = new BehaviorSubject<string>('');
  protected deleteFile$ = this.deleteFileSubject.asObservable();

  constructor(
    private fb: FormBuilder,
    private translateService: TranslateService,
    private taxYearDeclarationService: TaxYearDeclarationService,
    private taxDeclarationEndpointService: TaxYearDeclarationEndpointService,
    private storeService: StoreService,
    private router: Router,
    private modalService: SeModalService,
  ) {
    this.componentForm = this.fb.group({
      model: ['', Validators.required],
      codiImpost: ['', Validators.required],
      modelCode: ['', Validators.required],
      exercici: ['', Validators.required],
      anex: this.fb.group(
        {
          idPadoct: ['', Validators.required],
          id: ['', Validators.required],
          nom: ['', Validators.required],
          pes: ['', Validators.required],
          descripcio: ['', Validators.required],
          tipusDocument: ['', Validators.required],
          extension: ['', Validators.required],
        },
        Validators.required,
      ),
    });
  }

  ngOnInit(): void {
    this.getDeclarationTypes();
  }

  ngOnDestroy(): void {
    this.destroyed$.next();
    this.destroyed$.complete();
  }


  private simulateMouseClickToForceUpdate(): void {
    setTimeout(() => {
      const clickEvent = new MouseEvent('click', {
        view: window,
        bubbles: true,
        cancelable: true
      });
      document.dispatchEvent(clickEvent);
    }, 800);
  }

  private getDeclarationTypes(): void {
    this.taxDeclarationEndpointService
      .getDeclarationTypes()
      .pipe(takeUntil(this.destroyed$))
      .subscribe({
        next: (response) => {
          if (response?.content) {
            response.content.map((model: DeclarationTypes) => {
              this.optionsModelList.push({
                id: `${model.model}_${model.codiImpost}`,
                label: model.descripcio,
              });
            });
          }
        },
        error: (error) => {
          console.error('Error fetching declaration types:', error);
        },
      });
  }

  protected onExerciciChange(): void {
    if(this.componentForm.get('exercici')?.touched) {
      this.showDeleteDataWarningModal();
    }    
  }

  protected onModelChange(event: string): void {
    if (this.componentForm.get('model')?.value) {
      this.showDeleteDataWarningModal(true);
    } else {
      this.currentModelCode = event;
      const [model, codiImpost] = event.split('_');
      this.componentForm.get('model')?.setValue(model);
      this.componentForm.get('codiImpost')?.setValue(codiImpost);
      this.getTaxYearsByImpost(codiImpost);
    }
  }

  private showDeleteDataWarningModal(deleteModel: boolean = false): void {
    const modal: SeModal = this.taxYearDeclarationService.getWarningModalData();

    const modalRef = this.modalService.openModal(modal);
    
    modalRef.componentInstance.modalOutputEvent
      .pipe(takeUntil(this.destroyed$))
      .subscribe((event: string) => {
        
        if (event === SeModalOutputEvents.MAIN_ACTION) {
          this.deleteTaxDeclarationInformation(deleteModel);
          modalRef.close();
        } else {
          this.componentForm.get('modelCode')?.setValue(this.currentModelCode);
          modalRef.close();
        }
      });

      modalRef.componentInstance.modalSecondaryButtonEvent
      .pipe(takeUntil(this.destroyed$))
      .subscribe(() => {
        this.componentForm.get('modelCode')?.setValue(this.currentModelCode);
        modalRef.close();
      });

  }

  private deleteTaxDeclarationInformation(deleteModel: boolean): void {
    if (deleteModel) {
      this.componentForm.get('codiImpost')?.setValue('');
      this.componentForm.get('modelCode')?.setValue('');
      this.componentForm.get('model')?.setValue('');
      this.optionsTaxYearList = [];
    }
    this.componentForm.get('exercici')?.setValue('');

    const fileId = this.componentForm.get('anex')?.get('id')?.value;
    this.deleteFileSubject.next(fileId);
    this.componentForm.get('anex')?.reset();

    this.simulateMouseClickToForceUpdate();
  }

  private getTaxYearsByImpost(codiImpost: string): void {
    this.taxDeclarationEndpointService
      .getTaxYearsByImpost(codiImpost)
      .pipe(takeUntil(this.destroyed$))
      .subscribe({
        next: (response) => {
          if (response?.content) {
            this.optionsTaxYearList = response.content.map((year: number) => ({
              id: year,
              label: year.toString(),
            }));
          }
        },
        error: (error) => {
          console.error('Error fetching tax years:', error);
        },
      });
  }

  protected openProgressModal(): void {
    const progressValue$ = new BehaviorSubject<number>(1);

    const progressModal: SeProgressModal = {
      interval: 2,
      subtitle: '',
      message: this.translateService.instant(
        'SE_DECINF_MF.MODULE_TAX_YEAR_DECLARATION.PROGRESS_MODAL.DESCRIPTION',
      ),
      progressValue$: progressValue$,
    };

    const modalRef = this.modalService.openProgressModal(
      progressModal.interval!,
      progressModal.message!,
      progressModal.subtitle,
      progressModal.progressValue$,
    );

    // TODO Intervalo para pruebas hasta que tengamos el servicio real
    let value = 0;
    const intervalId = setInterval(() => {
      value += 20;
      progressValue$!.next(value);
      modalRef.componentInstance.progressValue = progressValue$;
      if (value >= 100) {
        progressValue$!.next(100);
        progressValue$!.complete();
        clearInterval(intervalId);
        modalRef.close();
        this.openSuccessModal();
      }
    }, 1000);

    //this.checkValidationProgress(modalRef, csvUploadBody, this.storeService.idTramit!); TODO LLAMADA REAL
  }

  /*  private checkValidationProgress = (
    modalRef: NgbModalRef,
    csvUploadBody: PutCsvUploaded,
    idTramit: string
  ): void => {
    modalRef.componentInstance.intervalOutput
      .pipe(takeUntil(this.destroyed$))
      .subscribe(() => {
        this.taxDeclarationEndpointService
          .putFileCsvUploaded(csvUploadBody, idTramit)
          .pipe(takeUntil(this.destroyed$))
          .subscribe({
            next: (response) => {
             console.log(response);
             
            },
            error: () => {
            },
          });
      });
  }; */

  protected onFilesLoaded(event: Event): void {
    const documentsAdded: iDocumentPadoct[] =
      (event as CustomEvent).detail || [];
    if (documentsAdded.length > 0) {
      const anexData = documentsAdded[0];
      this.componentForm.get('anex')?.patchValue({
        idPadoct: anexData.idPadoct,
        nom: anexData.nom,
        descripcio: anexData.description,
        pes: anexData.size,
        id: anexData.id,
        extension: anexData.format?.split('/')[1],
        tipusDocument: Constant.TAX_DOCUMENT_TYPE,
      });
    } else {
      this.componentForm.get('anex')?.reset();
    }
  }

  protected goBack(): void {
    this.router.navigate([AppRoutes.PARTICIPANTS]);
  }

  private openSuccessModal(): void {
    const modal: SeModal = {
      severity: 'success',
      title: this.translateService.instant(
        'SE_DECINF_MF.MODULE_TAX_YEAR_DECLARATION.PROGRESS_MODAL.SUCCESS.TITLE',
      ),
      subtitle: this.translateService.instant(
        'SE_DECINF_MF.MODULE_TAX_YEAR_DECLARATION.PROGRESS_MODAL.SUCCESS.DESCRIPTION',
      ),
      closable: true,
      closableLabel: this.translateService.instant(
        'UI_COMPONENTS.BUTTONS.CONTINUE',
      ),
      size: 'lg',
    };

    const modalRef = this.modalService.openModal(modal);
    modalRef.componentInstance.modalOutputEvent
      .pipe(takeUntil(this.destroyed$))
      .subscribe((event: string) => {
        if (event === SeModalOutputEvents.MAIN_ACTION) {
          modalRef.close();
          this.submit();
        }
      });
  }

  protected submit(): void {
    const idTramit = this.storeService.idTramit!;
    const model = this.componentForm.get('model')?.value;
    const exercici = this.componentForm.get('exercici')?.value;
    const codiImpost = this.componentForm.get('codiImpost')?.value;

    const taxYearAndModelBody = {
      idTramit: idTramit,
      model: model,
      codiImpost: codiImpost,
      exercici: exercici,
    };

    this.taxDeclarationEndpointService
      .putTaxYearAndModelDeclaration(taxYearAndModelBody)
      .pipe(takeUntil(this.destroyed$))
      .subscribe({
        next: () => {
          this.router.navigate([AppRoutes.SUMMARY]);
        },
        error: (error) => {
          console.error(
            'Error submitting tax year and model declaration:',
            error,
          );
        },
      });
  }
}
